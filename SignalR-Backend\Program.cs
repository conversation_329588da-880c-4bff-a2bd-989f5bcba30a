
using Microsoft.EntityFrameworkCore;
using SignalR_Backend.Hubs;
using SignalR_Backend.Models;

namespace SignalR_Backend
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddControllers();

            // Add SignalR
            builder.Services.AddSignalR();

            // Configure CORS for React app
            builder.Services.AddCors(options =>
            {
                options.AddPolicy("ReactApp", policy =>
                {
                    policy.WithOrigins("http://localhost:3000", "https://localhost:3000", "http://localhost:3001")
                          .AllowAnyMethod()
                          .AllowAnyHeader()
                          .AllowCredentials(); // Important for SignalR
                });
            });

            // Register EF Core with SQL Server
            builder.Services.AddDbContext<ChatDbContext>(options =>
                options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

            // Configure Swagger/OpenAPI
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
                {
                    Title = "SignalR Chat API",
                    Version = "v1",
                    Description = "A SignalR-based chat application API with real-time messaging capabilities",
                    Contact = new Microsoft.OpenApi.Models.OpenApiContact
                    {
                        Name = "SignalR Chat API",
                        Email = "<EMAIL>"
                    }
                });

                // Add XML comments if available
                var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                if (File.Exists(xmlPath))
                {
                    c.IncludeXmlComments(xmlPath);
                }
            });

            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SignalR Chat API v1");
                    c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger
                    c.DocumentTitle = "SignalR Chat API Documentation";
                    c.DefaultModelsExpandDepth(-1); // Hide models section by default
                });
                app.UseDeveloperExceptionPage();
            }

            app.UseHttpsRedirection();

            // Use CORS before other middleware
            app.UseCors("ReactApp");

            app.UseAuthorization();

            app.MapControllers();

            // Map SignalR hub
            app.MapHub<ChatHub>("/chatHub");

            app.Run();
        }
    }
}
