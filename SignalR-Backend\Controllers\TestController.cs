using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using SignalR_Backend.Hubs;
using System.ComponentModel.DataAnnotations;

namespace SignalR_Backend.Controllers
{
    /// <summary>
    /// Test controller for SignalR Chat API functionality
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class TestController : ControllerBase
    {
        private readonly IHubContext<ChatHub> _hubContext;

        public TestController(IHubContext<ChatHub> hubContext)
        {
            _hubContext = hubContext;
        }

        /// <summary>
        /// Health check endpoint to verify the API is running
        /// </summary>
        /// <returns>API status and current timestamp</returns>
        /// <response code="200">API is running successfully</response>
        [HttpGet("ping")]
        [ProducesResponseType(typeof(object), 200)]
        public IActionResult Ping()
        {
            return Ok(new
            {
                message = "SignalR Backend is running!",
                timestamp = DateTime.UtcNow,
                version = "1.0.0",
                signalRHub = "/chatHub"
            });
        }

        /// <summary>
        /// Broadcast a test message to all connected SignalR clients
        /// </summary>
        /// <param name="testMessage">The message to broadcast</param>
        /// <returns>Success or error response</returns>
        /// <response code="200">Message broadcasted successfully</response>
        /// <response code="400">Invalid request or broadcast failed</response>
        [HttpPost("broadcast")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(typeof(object), 400)]
        public async Task<IActionResult> BroadcastMessage([FromBody] TestMessage testMessage)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { success = false, error = "Invalid message data", details = ModelState });
            }

            try
            {
                await _hubContext.Clients.All.SendAsync("ReceiveMessage",
                    testMessage.User,
                    testMessage.Message,
                    DateTime.UtcNow);

                return Ok(new
                {
                    success = true,
                    message = "Message broadcasted successfully",
                    broadcastedAt = DateTime.UtcNow,
                    user = testMessage.User,
                    content = testMessage.Message
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, error = ex.Message });
            }
        }
    }

    /// <summary>
    /// Test message model for broadcasting via SignalR
    /// </summary>
    public class TestMessage
    {
        /// <summary>
        /// The username of the message sender
        /// </summary>
        /// <example>John Doe</example>
        [Required(ErrorMessage = "User name is required")]
        [StringLength(100, MinimumLength = 1, ErrorMessage = "User name must be between 1 and 100 characters")]
        public string User { get; set; } = string.Empty;

        /// <summary>
        /// The message content to broadcast
        /// </summary>
        /// <example>Hello, this is a test message!</example>
        [Required(ErrorMessage = "Message content is required")]
        [StringLength(1000, MinimumLength = 1, ErrorMessage = "Message must be between 1 and 1000 characters")]
        public string Message { get; set; } = string.Empty;
    }
}
