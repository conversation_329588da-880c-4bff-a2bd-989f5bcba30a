using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using SignalR_Backend.Hubs;

namespace SignalR_Backend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly IHubContext<ChatHub> _hubContext;

        public TestController(IHubContext<ChatHub> hubContext)
        {
            _hubContext = hubContext;
        }

        [HttpGet("ping")]
        public IActionResult Ping()
        {
            return Ok(new { message = "SignalR Backend is running!", timestamp = DateTime.UtcNow });
        }

        [HttpPost("broadcast")]
        public async Task<IActionResult> BroadcastMessage([FromBody] TestMessage testMessage)
        {
            try
            {
                await _hubContext.Clients.All.SendAsync("ReceiveMessage", 
                    testMessage.User, 
                    testMessage.Message, 
                    DateTime.UtcNow);
                
                return Ok(new { success = true, message = "Message broadcasted successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, error = ex.Message });
            }
        }
    }

    public class TestMessage
    {
        public string User { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }
}
